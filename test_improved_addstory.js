/**
 * 测试改进的 addStory 功能
 */

const { addStoryImproved } = require('./addstory_improved');
const fs = require('fs');

async function testImprovedAddStory() {
    console.log('🧪 测试改进版addStory功能\n');
    
    // 检查认证文件
    if (!fs.existsSync('auth.txt')) {
        console.log('❌ 未找到 auth.txt 文件');
        console.log('\n📋 请先设置认证信息:');
        console.log('1. 创建 auth.txt 文件');
        console.log('2. 在文件中添加你的OAuth认证字符串');
        console.log('3. 格式: OAuth oauth_consumer_key="...", oauth_signature="..."');
        console.log('\n💡 获取认证信息的方法:');
        console.log('1. 在浏览器中打开深海传说游戏');
        console.log('2. 按F12打开开发者工具');
        console.log('3. 切换到Network标签');
        console.log('4. 在游戏中查看Story');
        console.log('5. 找到getResource请求，复制Authorization头的值');
        return;
    }
    
    console.log('✅ 找到认证文件\n');
    
    const testStoryId = process.argv[2] || '100503';
    console.log(`📖 测试Story ID: ${testStoryId}\n`);
    
    console.log('🚀 开始测试改进版addStory...\n');
    console.log('=' .repeat(60));
    
    try {
        const result = await addStoryImproved(testStoryId);
        
        console.log('=' .repeat(60));
        console.log('✅ 测试成功！改进版addStory工作正常');
        console.log('\n📊 测试结果:');
        console.log('- addStory调用成功');
        console.log('- 服务器响应正常');
        console.log('- 数据格式正确');
        
        if (result && result.status) {
            console.log(`- 服务器状态: ${result.status}`);
        }
        
    } catch (error) {
        console.log('=' .repeat(60));
        console.log('❌ 测试失败:');
        console.log(`错误信息: ${error.message}`);
        
        console.log('\n🔧 可能的解决方案:');
        console.log('1. 检查网络连接');
        console.log('2. 验证auth.txt中的认证信息是否有效');
        console.log('3. 尝试不同的Story ID');
        console.log('4. 检查游戏服务器是否可访问');
        console.log('5. 确认API端点是否正确');
        
        console.log('\n🔍 调试信息:');
        console.log('- 改进版addStory尝试了多种方法');
        console.log('- 如果所有方法都失败，可能是API端点或认证问题');
        console.log('- 建议检查浏览器开发者工具中的实际API调用');
    }
    
    console.log('\n💡 提示:');
    console.log('- 改进版addStory会自动尝试多种方法');
    console.log('- 包括不同的API路径和请求格式');
    console.log('- 如果仍然失败，可能需要分析真实的客户端请求');
    
    console.log('\n📚 下一步:');
    console.log('- 如果addStory成功，可以继续测试getResource');
    console.log('- 运行: node get_story.js <storyId>');
    console.log('- 或者: node test_improved_addstory.js <storyId>');
}

// 比较测试：同时测试原版和改进版
async function compareAddStoryMethods() {
    console.log('🔄 比较测试：原版 vs 改进版 addStory\n');
    
    const testStoryId = process.argv[2] || '100503';
    
    console.log('📖 测试1: 改进版addStory');
    try {
        const improvedResult = await addStoryImproved(testStoryId);
        console.log('✅ 改进版成功');
        console.log('📄 改进版结果:', JSON.stringify(improvedResult, null, 2));
    } catch (error) {
        console.log('❌ 改进版失败:', error.message);
    }
    
    console.log('\n' + '='.repeat(50) + '\n');
    
    console.log('📖 测试2: 原版addStory');
    try {
        const { addStory } = require('./get_story');
        const originalResult = await addStory(testStoryId);
        console.log('✅ 原版成功');
        console.log('📄 原版结果:', JSON.stringify(originalResult, null, 2));
    } catch (error) {
        console.log('❌ 原版失败:', error.message);
    }
}

// 主函数
async function main() {
    const command = process.argv[3];
    
    if (command === 'compare') {
        await compareAddStoryMethods();
    } else {
        await testImprovedAddStory();
    }
}

if (require.main === module) {
    main().catch(console.error);
}

module.exports = { testImprovedAddStory, compareAddStoryMethods };
