# 深海传说 Story获取工具 - addStory功能说明

## 新增功能

现在 `get_story.js` 工具已经增强，会在获取Story资源前自动调用 `addStory` 方法来添加故事到用户账户。

## 工作流程

1. **步骤1**: 调用 `addStory` API 添加故事到用户账户
2. **步骤2**: 调用 `getResource` API 获取故事资源

## 使用方法

### 基本使用（与之前相同）

```bash
# 获取单个Story
node get_story.js 100503

# 获取多个Story
node get_story.js 100501 100502 100503

# 获取范围内的Story
node get_story.js 100501-100505
```

### 新的执行流程

当你运行 `node get_story.js 100503` 时，你会看到：

```
🎯 获取Story ID: 100503

📝 步骤1: 先添加Story到用户账户...
📝 添加Story ID: 100503
🚀 发送addStory请求...
✅ 成功添加Story！
✅ Story添加成功，现在获取资源...

📥 步骤2: 获取Story资源...
🚀 发送请求...
✅ 成功获取Story数据！
📁 数据已保存到: story_100503_1234567890.json
```

## API 实现细节

### addStory API

- **端点**: `/deep-one/api/story/addStory`
- **方法**: POST
- **请求体**:
  ```json
  {
    "storyId": "100503"
  }
  ```

### 错误处理

如果 `addStory` 失败，工具会：
1. 显示警告信息
2. 继续执行 `getResource` 步骤
3. 不会中断整个流程

```
⚠️  addStory失败: HTTP 404: Not Found
🔄 继续尝试获取资源...
```

## 测试功能

运行测试脚本来验证功能：

```bash
node test_addstory.js
```

测试脚本会：
1. 单独测试 `addStory` 功能
2. 测试完整的 `addStory` + `getResource` 流程
3. 提供调试信息

## 注意事项

### 1. API端点推测

`addStory` 的API端点是基于常见的RESTful API模式推测的：
- `/deep-one/api/story/addStory`

如果这个端点不正确，你可能会看到404错误，但这不会影响 `getResource` 的正常工作。

### 2. 认证要求

`addStory` 使用与 `getResource` 相同的认证信息（OAuth），确保 `auth.txt` 文件包含有效的认证信息。

### 3. 兼容性

这个更新完全向后兼容，现有的使用方式不会改变，只是增加了额外的 `addStory` 步骤。

## 故障排除

### addStory失败但getResource成功

这是正常情况，可能的原因：
- API端点路径不正确
- Story已经在用户账户中
- 服务器不需要显式添加Story

### 两个步骤都失败

检查：
1. `auth.txt` 文件是否存在且包含有效认证信息
2. 网络连接是否正常
3. Story ID是否有效

## 自定义配置

如果需要修改 `addStory` 的API端点，编辑 `get_story.js` 文件中的：

```javascript
path: '/deep-one/api/story/addStory',  // 修改这里
```

## 参考

基于 `client/1.273.0/src/project.js` 中的相应函数实现，遵循游戏客户端的API调用模式。
