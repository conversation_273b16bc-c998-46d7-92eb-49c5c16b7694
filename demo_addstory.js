/**
 * 深海传说 addStory功能演示
 */

const { getStory, addStory } = require('./get_story');
const fs = require('fs');

async function demo() {
    console.log('🌊 深海传说 Story获取工具 - addStory功能演示\n');
    
    // 检查认证文件
    if (!fs.existsSync('auth.txt')) {
        console.log('❌ 未找到 auth.txt 文件');
        console.log('\n📋 请先设置认证信息:');
        console.log('1. 创建 auth.txt 文件');
        console.log('2. 在文件中添加你的OAuth认证字符串');
        console.log('3. 格式: OAuth oauth_consumer_key="...", oauth_signature="..."');
        console.log('\n💡 获取认证信息的方法:');
        console.log('1. 在浏览器中打开深海传说游戏');
        console.log('2. 按F12打开开发者工具');
        console.log('3. 切换到Network标签');
        console.log('4. 在游戏中查看Story');
        console.log('5. 找到getResource请求，复制Authorization头的值');
        return;
    }
    
    console.log('✅ 找到认证文件\n');
    
    // 演示Story ID
    const demoStoryId = '100503';
    
    console.log('📖 演示说明:');
    console.log('- 新版本会先调用addStory添加故事到账户');
    console.log('- 然后调用getResource获取故事资源');
    console.log('- 即使addStory失败，getResource仍会继续执行');
    console.log(`- 演示Story ID: ${demoStoryId}\n`);
    
    console.log('🚀 开始演示...\n');
    console.log('=' .repeat(50));
    
    try {
        // 使用新的getStory函数（包含addStory步骤）
        const result = await getStory(demoStoryId);
        
        console.log('=' .repeat(50));
        console.log('✅ 演示完成！');
        
        if (result) {
            console.log('\n📊 结果摘要:');
            console.log(`- Story ID: ${demoStoryId}`);
            console.log(`- 数据类型: ${typeof result}`);
            
            if (result.resource && Array.isArray(result.resource)) {
                console.log(`- 资源文件数量: ${result.resource.length}`);
                
                // 显示资源类型统计
                const textFiles = result.resource.filter(r => r.fileName && r.fileName.endsWith('.txt'));
                const audioFiles = result.resource.filter(r => r.fileName && r.fileName.endsWith('.mp3'));
                const imageFiles = result.resource.filter(r => r.fileName && r.fileName.match(/\.(jpg|png|webp)$/));
                
                if (textFiles.length > 0) console.log(`- 文本文件: ${textFiles.length} 个`);
                if (audioFiles.length > 0) console.log(`- 音频文件: ${audioFiles.length} 个`);
                if (imageFiles.length > 0) console.log(`- 图片文件: ${imageFiles.length} 个`);
            }
        }
        
    } catch (error) {
        console.log('=' .repeat(50));
        console.log('❌ 演示过程中出现错误:');
        console.log(`错误信息: ${error.message}`);
        
        console.log('\n🔧 可能的解决方案:');
        console.log('1. 检查网络连接');
        console.log('2. 验证auth.txt中的认证信息是否有效');
        console.log('3. 尝试不同的Story ID');
        console.log('4. 检查游戏服务器是否可访问');
    }
    
    console.log('\n💡 提示:');
    console.log('- addStory API端点是推测的，可能需要调整');
    console.log('- 主要功能是确保getResource正常工作');
    console.log('- 如果addStory失败，这是正常的，不影响资源获取');
    
    console.log('\n📚 更多信息请查看:');
    console.log('- ADDSTORY_USAGE.md - 详细使用说明');
    console.log('- test_addstory.js - 功能测试脚本');
}

// 单独演示addStory功能
async function demoAddStoryOnly() {
    console.log('\n🔍 单独测试addStory功能...\n');
    
    const testStoryId = '100503';
    
    try {
        console.log('📝 调用addStory...');
        const result = await addStory(testStoryId);
        console.log('✅ addStory成功:', result);
        
    } catch (error) {
        console.log('❌ addStory失败:', error.message);
        console.log('💡 这可能是正常的，因为API端点是推测的');
    }
}

// 主函数
async function main() {
    await demo();
    
    // 询问是否要单独测试addStory
    console.log('\n❓ 是否要单独测试addStory功能？');
    console.log('💡 运行: node -e "require(\'./demo_addstory\').demoAddStoryOnly()"');
}

if (require.main === module) {
    main().catch(console.error);
}

module.exports = { demo, demoAddStoryOnly };
