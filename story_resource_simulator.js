/**
 * 深海传说 (Deep One) 客户端Story资源获取模拟器
 * 
 * 基于客户端源码中的getStoryResource函数实现
 * 原函数: getStoryResource:function(t,e,i){var a={storyIds:t.join(","),adult:e||0};this._requestCallApi(n.STORY_GET_RESOURCE,a,function(t,e){return i(t?void 0:e)}
 */

const https = require('https');
const http = require('http');
const querystring = require('querystring');

class StoryResourceSimulator {
    constructor(options = {}) {
        // 游戏服务器配置 - 基于真实请求头
        this.baseUrl = options.baseUrl || 'https://tonofura-web-w.deepone-online.com';
        this.apiPath = options.apiPath || '/deep-one/api/story/getResource';

        // 请求头配置 - 基于真实浏览器请求
        this.headers = {
            'User-Agent': 'Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36 Edg/*********',
            'Content-Type': 'application/json;charset=UTF-8',
            'Accept': 'application/json;charset=UTF-8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6',
            'Accept-Encoding': 'gzip, deflate, br, zstd',
            'Cache-Control': 'no-cache',
            'Pragma': 'no-cache',
            'Origin': 'https://tonofura-w-cdn-client.deepone-online.com',
            'Referer': 'https://tonofura-w-cdn-client.deepone-online.com/',
            'Sec-Ch-Ua': '"Not;A=Brand";v="99", "Microsoft Edge";v="139", "Chromium";v="139"',
            'Sec-Ch-Ua-Mobile': '?1',
            'Sec-Ch-Ua-Platform': '"Android"',
            'Sec-Fetch-Dest': 'empty',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Site': 'same-site',
            'Priority': 'u=1, i',
            'X-Deep-One-App-Version': '{"masterVersion":"1.273.0","webVersion":"1.273.0","apkHotUpdateVersion":"1.273.0"}',
            ...options.headers
        };

        // OAuth认证信息
        this.oauthConfig = {
            oauth_token: options.oauth_token || null,
            xoauth_requestor_id: options.xoauth_requestor_id || null,
            oauth_consumer_key: options.oauth_consumer_key || '*********************************',
            oauth_signature_method: 'HMAC-SHA256',
            oauth_nonce: null,
            oauth_timestamp: null,
            oauth_signature: null,
            ...options.oauthConfig
        };

        // 传统认证信息（备用）
        this.sessionToken = options.sessionToken || null;
        this.userId = options.userId || null;
    }

    /**
     * 模拟客户端的getStoryResource函数
     * @param {Array<string|number>} storyIds - Story ID数组
     * @param {number} adult - 成人内容标志 (0或1)
     * @returns {Promise<Object>} Story资源数据
     */
    async getStoryResource(storyIds, adult = 0) {
        // 验证参数
        if (!Array.isArray(storyIds) || storyIds.length === 0) {
            throw new Error('storyIds must be a non-empty array');
        }

        // 构建请求参数 - 基于真实请求格式 (JSON)
        const requestBody = {
            storyIds: storyIds.join(','),
            adult: adult || 0
        };

        try {
            const response = await this._makeRequest(requestBody);
            return response;
        } catch (error) {
            console.error('获取Story资源失败:', error.message);
            throw error;
        }
    }

    /**
     * 生成OAuth签名
     * @param {string} method - HTTP方法
     * @param {string} url - 请求URL
     * @param {Object} params - OAuth参数
     * @returns {string} OAuth签名
     */
    _generateOAuthSignature(method, url, params) {
        // 这里需要实现OAuth 1.0a签名算法
        // 由于需要consumer secret，这里返回一个占位符
        // 实际使用时需要从真实请求中获取有效的签名
        return this.oauthConfig.oauth_signature || 'PLACEHOLDER_SIGNATURE';
    }

    /**
     * 生成OAuth Authorization头
     * @returns {string} Authorization头值
     */
    _generateAuthorizationHeader() {
        if (!this.oauthConfig.oauth_token) {
            return null;
        }

        // 使用提供的参数，如果没有时间戳和nonce则生成新的
        const timestamp = this.oauthConfig.oauth_timestamp || Math.floor(Date.now() / 1000).toString();
        const nonce = this.oauthConfig.oauth_nonce || (Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15));
        const signature = this.oauthConfig.oauth_signature || this._generateOAuthSignature('POST', this.baseUrl + this.apiPath, {});

        const oauthParams = {
            oauth_token: this.oauthConfig.oauth_token,
            xoauth_requestor_id: this.oauthConfig.xoauth_requestor_id,
            oauth_consumer_key: this.oauthConfig.oauth_consumer_key,
            oauth_signature_method: this.oauthConfig.oauth_signature_method,
            oauth_nonce: nonce,
            oauth_timestamp: timestamp,
            oauth_signature: signature
        };

        // 构建Authorization头，注意不要重复转义引号
        const authParts = Object.entries(oauthParams)
            .filter(([key, value]) => value !== null && value !== undefined)
            .map(([key, value]) => `${key}="${value}"`)
            .join(' ');

        return `OAuth realm="Users" ${authParts}`;
    }

    /**
     * 发送HTTP请求
     * @param {Object} requestBody - 请求体数据
     * @returns {Promise<Object>} 响应数据
     */
    _makeRequest(requestBody) {
        return new Promise((resolve, reject) => {
            const postData = JSON.stringify(requestBody);

            const url = new URL(this.apiPath, this.baseUrl);
            const isHttps = url.protocol === 'https:';
            const httpModule = isHttps ? https : http;

            // 准备请求头
            const requestHeaders = {
                ...this.headers,
                'Content-Length': Buffer.byteLength(postData)
            };

            // 添加OAuth认证头
            const authHeader = this._generateAuthorizationHeader();
            if (authHeader) {
                requestHeaders['Authorization'] = authHeader;
            }

            const options = {
                hostname: url.hostname,
                port: url.port || (isHttps ? 443 : 80),
                path: url.pathname + url.search,
                method: 'POST',
                headers: requestHeaders
            };

            console.log('请求URL:', url.toString());
            console.log('请求体:', postData);
            console.log('请求头:', JSON.stringify(requestHeaders, null, 2));

            const req = httpModule.request(options, (res) => {
                let data = '';

                res.on('data', (chunk) => {
                    data += chunk;
                });

                res.on('end', () => {
                    console.log('响应状态码:', res.statusCode);
                    console.log('响应数据:', data);

                    try {
                        const jsonData = JSON.parse(data);

                        if (res.statusCode === 200) {
                            resolve(jsonData);
                        } else {
                            reject(new Error(`HTTP ${res.statusCode}: ${jsonData.message || data}`));
                        }
                    } catch (parseError) {
                        reject(new Error(`JSON解析失败: ${parseError.message}\n原始响应: ${data}`));
                    }
                });
            });

            req.on('error', (error) => {
                reject(new Error(`请求失败: ${error.message}`));
            });

            req.write(postData);
            req.end();
        });
    }

    /**
     * 批量获取多个Story资源
     * @param {Array<Array>} storyIdGroups - Story ID组数组
     * @param {number} adult - 成人内容标志
     * @returns {Promise<Array>} Story资源数据数组
     */
    async getMultipleStoryResources(storyIdGroups, adult = 0) {
        const promises = storyIdGroups.map(group => 
            this.getStoryResource(group, adult)
        );
        
        try {
            return await Promise.all(promises);
        } catch (error) {
            console.error('批量获取Story资源失败:', error.message);
            throw error;
        }
    }

    /**
     * 设置OAuth认证信息
     * @param {Object} oauthConfig - OAuth配置
     */
    setOAuth(oauthConfig) {
        this.oauthConfig = {
            ...this.oauthConfig,
            ...oauthConfig
        };
    }

    /**
     * 设置认证信息（传统方式，备用）
     * @param {string} sessionToken - 会话令牌
     * @param {string} userId - 用户ID
     */
    setAuth(sessionToken, userId) {
        this.sessionToken = sessionToken;
        this.userId = userId;
    }
}

// 使用示例
async function example() {
    // 创建模拟器实例
    const simulator = new StoryResourceSimulator({
        // 根据实际情况配置服务器地址
        baseUrl: 'https://tonofura-w-cdn-client.deepone-online.com',
        // 可能需要的认证头
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    });

    // 设置认证信息（如果需要）
    // simulator.setAuth('your_session_token', 'your_user_id');

    try {
        // 获取单个Story资源
        const storyData = await simulator.getStoryResource(['1001'], 0);
        console.log('Story资源:', JSON.stringify(storyData, null, 2));

        // 批量获取多个Story资源
        const multipleStories = await simulator.getMultipleStoryResources([
            ['1001'],
            ['1002', '1003'],
            ['1004']
        ], 0);
        console.log('批量Story资源:', multipleStories);

    } catch (error) {
        console.error('获取失败:', error.message);
    }
}

module.exports = StoryResourceSimulator;

// 如果直接运行此文件，执行示例
if (require.main === module) {
    example();
}
