/**
 * 分析 addStory 问题的脚本
 * 帮助理解 "picture book detail not found" 错误
 */

const https = require('https');
const fs = require('fs');

// 读取认证信息
function getAuthFromFile() {
    try {
        const authData = fs.readFileSync('auth.txt', 'utf8').trim();
        return authData;
    } catch (error) {
        return null;
    }
}

// 分析错误响应
function analyzeErrorResponse(statusCode, responseData) {
    console.log('\n🔍 错误分析:');
    console.log(`HTTP状态码: ${statusCode}`);
    console.log(`响应数据: ${responseData}`);
    
    try {
        const jsonData = JSON.parse(responseData);
        console.log('📄 解析后的JSON:');
        console.log(JSON.stringify(jsonData, null, 2));
        
        if (jsonData.status === 'err' && jsonData.message) {
            console.log(`\n❌ 服务器错误: ${jsonData.message}`);
            
            if (jsonData.message.includes('picture book detail not found')) {
                console.log('\n💡 分析: "picture book detail not found" 错误');
                console.log('这个错误表明:');
                console.log('1. 服务器期望先有 pictureBookDetail 数据');
                console.log('2. 可能需要先调用 getPictureBookDetail API');
                console.log('3. 或者 addStory 需要包含 pictureBookDetail 信息');
                console.log('4. 也可能是 Story ID 不存在或无权访问');
            }
        }
    } catch (parseError) {
        console.log('❌ 无法解析JSON响应');
    }
}

// 测试不同的API端点
async function testDifferentEndpoints(storyId, authHeader) {
    const endpoints = [
        '/deep-one/api/story/addStory',
        '/deep-one/api/story/add',
        '/deep-one/api/story/addToLibrary',
        '/deep-one/api/story/purchase',
        '/deep-one/api/story/unlock'
    ];
    
    console.log('\n🔄 测试不同的API端点...\n');
    
    for (const endpoint of endpoints) {
        console.log(`📡 测试端点: ${endpoint}`);
        
        try {
            const result = await testEndpoint(storyId, authHeader, endpoint);
            console.log(`✅ 端点 ${endpoint} 成功!`);
            console.log('📄 响应:', JSON.stringify(result, null, 2));
            return { endpoint, result };
        } catch (error) {
            console.log(`❌ 端点 ${endpoint} 失败: ${error.message}`);
            
            // 分析错误
            if (error.message.includes('HTTP')) {
                const match = error.message.match(/HTTP (\d+): (.+)/);
                if (match) {
                    analyzeErrorResponse(parseInt(match[1]), match[2]);
                }
            }
        }
        
        console.log(''); // 空行分隔
    }
    
    return null;
}

// 测试单个端点
async function testEndpoint(storyId, authHeader, endpoint) {
    const requestBody = JSON.stringify({
        storyId: storyId
    });

    const options = {
        hostname: 'tonofura-web-w.deepone-online.com',
        port: 443,
        path: endpoint,
        method: 'POST',
        headers: {
            'User-Agent': 'Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36 Edg/*********',
            'Content-Type': 'application/json;charset=UTF-8',
            'Accept': 'application/json;charset=UTF-8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6',
            'Cache-Control': 'no-cache',
            'Pragma': 'no-cache',
            'Origin': 'https://tonofura-w-cdn-client.deepone-online.com',
            'Referer': 'https://tonofura-w-cdn-client.deepone-online.com/',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Site': 'same-site',
            'X-Deep-One-App-Version': '{"masterVersion":"1.273.0","webVersion":"1.273.0","apkHotUpdateVersion":"1.273.0"}',
            'Content-Length': Buffer.byteLength(requestBody),
            'Authorization': authHeader
        }
    };

    return new Promise((resolve, reject) => {
        const req = https.request(options, (res) => {
            let data = '';

            res.on('data', (chunk) => {
                data += chunk;
            });

            res.on('end', () => {
                if (res.statusCode === 200) {
                    try {
                        const jsonData = JSON.parse(data);
                        resolve(jsonData);
                    } catch (parseError) {
                        reject(parseError);
                    }
                } else {
                    reject(new Error(`HTTP ${res.statusCode}: ${data}`));
                }
            });
        });

        req.on('error', (error) => {
            reject(error);
        });

        req.write(requestBody);
        req.end();
    });
}

// 测试不同的请求格式
async function testDifferentFormats(storyId, authHeader) {
    const formats = [
        { name: '字符串格式', data: { storyId: storyId } },
        { name: '数字格式', data: { storyId: parseInt(storyId) } },
        { name: '带adult参数', data: { storyId: storyId, adult: 0 } },
        { name: '带adult参数(数字)', data: { storyId: parseInt(storyId), adult: 0 } },
        { name: '数组格式', data: { storyIds: [storyId] } },
        { name: '逗号分隔格式', data: { storyIds: storyId } }
    ];
    
    console.log('\n🔄 测试不同的请求格式...\n');
    
    for (const format of formats) {
        console.log(`📝 测试格式: ${format.name}`);
        console.log(`📄 请求数据: ${JSON.stringify(format.data)}`);
        
        try {
            const result = await testFormat(format.data, authHeader);
            console.log(`✅ 格式 ${format.name} 成功!`);
            console.log('📄 响应:', JSON.stringify(result, null, 2));
            return { format: format.name, result };
        } catch (error) {
            console.log(`❌ 格式 ${format.name} 失败: ${error.message}`);
        }
        
        console.log(''); // 空行分隔
    }
    
    return null;
}

// 测试单个格式
async function testFormat(requestData, authHeader) {
    const requestBody = JSON.stringify(requestData);

    const options = {
        hostname: 'tonofura-web-w.deepone-online.com',
        port: 443,
        path: '/deep-one/api/story/addStory',
        method: 'POST',
        headers: {
            'User-Agent': 'Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36 Edg/*********',
            'Content-Type': 'application/json;charset=UTF-8',
            'Accept': 'application/json;charset=UTF-8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6',
            'Cache-Control': 'no-cache',
            'Pragma': 'no-cache',
            'Origin': 'https://tonofura-w-cdn-client.deepone-online.com',
            'Referer': 'https://tonofura-w-cdn-client.deepone-online.com/',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Site': 'same-site',
            'X-Deep-One-App-Version': '{"masterVersion":"1.273.0","webVersion":"1.273.0","apkHotUpdateVersion":"1.273.0"}',
            'Content-Length': Buffer.byteLength(requestBody),
            'Authorization': authHeader
        }
    };

    return new Promise((resolve, reject) => {
        const req = https.request(options, (res) => {
            let data = '';

            res.on('data', (chunk) => {
                data += chunk;
            });

            res.on('end', () => {
                if (res.statusCode === 200) {
                    try {
                        const jsonData = JSON.parse(data);
                        resolve(jsonData);
                    } catch (parseError) {
                        reject(parseError);
                    }
                } else {
                    reject(new Error(`HTTP ${res.statusCode}: ${data}`));
                }
            });
        });

        req.on('error', (error) => {
            reject(error);
        });

        req.write(requestBody);
        req.end();
    });
}

// 主分析函数
async function analyzeAddStoryIssue() {
    console.log('🔍 深海传说 addStory 问题分析\n');
    
    // 检查认证文件
    const authHeader = getAuthFromFile();
    if (!authHeader) {
        console.log('❌ 未找到 auth.txt 文件');
        console.log('请先设置认证信息后再运行分析');
        return;
    }
    
    console.log('✅ 找到认证文件');
    
    const testStoryId = process.argv[2] || '100503';
    console.log(`📖 分析Story ID: ${testStoryId}\n`);
    
    console.log('🚀 开始全面分析...\n');
    console.log('=' .repeat(60));
    
    // 测试1: 不同的API端点
    const endpointResult = await testDifferentEndpoints(testStoryId, authHeader);
    
    console.log('=' .repeat(60));
    
    // 测试2: 不同的请求格式
    const formatResult = await testDifferentFormats(testStoryId, authHeader);
    
    console.log('=' .repeat(60));
    console.log('\n📊 分析总结:');
    
    if (endpointResult) {
        console.log(`✅ 找到工作的API端点: ${endpointResult.endpoint}`);
    } else {
        console.log('❌ 没有找到工作的API端点');
    }
    
    if (formatResult) {
        console.log(`✅ 找到工作的请求格式: ${formatResult.format}`);
    } else {
        console.log('❌ 没有找到工作的请求格式');
    }
    
    console.log('\n💡 建议:');
    console.log('1. 检查浏览器开发者工具中的真实API调用');
    console.log('2. 确认Story ID是否存在且可访问');
    console.log('3. 验证认证信息是否有效');
    console.log('4. 考虑是否需要先调用其他API（如getPictureBookDetail）');
}

if (require.main === module) {
    analyzeAddStoryIssue().catch(console.error);
}

module.exports = { analyzeAddStoryIssue, testDifferentEndpoints, testDifferentFormats };
