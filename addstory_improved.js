/**
 * 改进的 addStory 实现
 * 基于对客户端逻辑的深入分析
 */

const https = require('https');
const fs = require('fs');

// 读取认证信息
function getAuthFromFile() {
    try {
        const authData = fs.readFileSync('auth.txt', 'utf8').trim();
        return authData;
    } catch (error) {
        return null;
    }
}

// 方法1: 直接添加Story（最简单的方法）
async function tryAddStoryDirect(storyId, authHeader) {
    console.log('🔄 尝试直接添加Story...');
    
    const requestBody = JSON.stringify({
        storyId: storyId
    });

    const options = {
        hostname: 'tonofura-web-w.deepone-online.com',
        port: 443,
        path: '/deep-one/api/story/addStory',
        method: 'POST',
        headers: {
            'User-Agent': 'Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36 Edg/*********',
            'Content-Type': 'application/json;charset=UTF-8',
            'Accept': 'application/json;charset=UTF-8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6',
            'Cache-Control': 'no-cache',
            'Pragma': 'no-cache',
            'Origin': 'https://tonofura-w-cdn-client.deepone-online.com',
            'Referer': 'https://tonofura-w-cdn-client.deepone-online.com/',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Site': 'same-site',
            'X-Deep-One-App-Version': '{"masterVersion":"1.273.0","webVersion":"1.273.0","apkHotUpdateVersion":"1.273.0"}',
            'Content-Length': Buffer.byteLength(requestBody),
            'Authorization': authHeader
        }
    };

    return new Promise((resolve, reject) => {
        const req = https.request(options, (res) => {
            let data = '';

            res.on('data', (chunk) => {
                data += chunk;
            });

            res.on('end', () => {
                if (res.statusCode === 200) {
                    try {
                        const jsonData = JSON.parse(data);
                        console.log('✅ 直接添加Story成功！');
                        resolve(jsonData);
                    } catch (parseError) {
                        reject(parseError);
                    }
                } else {
                    console.log(`❌ 直接添加Story失败 (HTTP ${res.statusCode}): ${data}`);
                    reject(new Error(`HTTP ${res.statusCode}: ${data}`));
                }
            });
        });

        req.on('error', (error) => {
            reject(error);
        });

        req.write(requestBody);
        req.end();
    });
}

// 方法2: 尝试不同的API路径
async function tryAddStoryAlternatePath(storyId, authHeader) {
    console.log('🔄 尝试备用API路径...');
    
    const requestBody = JSON.stringify({
        storyId: storyId
    });

    const options = {
        hostname: 'tonofura-web-w.deepone-online.com',
        port: 443,
        path: '/deep-one/api/story/add',  // 备用路径
        method: 'POST',
        headers: {
            'User-Agent': 'Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36 Edg/*********',
            'Content-Type': 'application/json;charset=UTF-8',
            'Accept': 'application/json;charset=UTF-8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6',
            'Cache-Control': 'no-cache',
            'Pragma': 'no-cache',
            'Origin': 'https://tonofura-w-cdn-client.deepone-online.com',
            'Referer': 'https://tonofura-w-cdn-client.deepone-online.com/',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Site': 'same-site',
            'X-Deep-One-App-Version': '{"masterVersion":"1.273.0","webVersion":"1.273.0","apkHotUpdateVersion":"1.273.0"}',
            'Content-Length': Buffer.byteLength(requestBody),
            'Authorization': authHeader
        }
    };

    return new Promise((resolve, reject) => {
        const req = https.request(options, (res) => {
            let data = '';

            res.on('data', (chunk) => {
                data += chunk;
            });

            res.on('end', () => {
                if (res.statusCode === 200) {
                    try {
                        const jsonData = JSON.parse(data);
                        console.log('✅ 备用路径添加Story成功！');
                        resolve(jsonData);
                    } catch (parseError) {
                        reject(parseError);
                    }
                } else {
                    console.log(`❌ 备用路径添加Story失败 (HTTP ${res.statusCode}): ${data}`);
                    reject(new Error(`HTTP ${res.statusCode}: ${data}`));
                }
            });
        });

        req.on('error', (error) => {
            reject(error);
        });

        req.write(requestBody);
        req.end();
    });
}

// 方法3: 尝试不同的请求格式
async function tryAddStoryDifferentFormat(storyId, authHeader) {
    console.log('🔄 尝试不同的请求格式...');
    
    const requestBody = JSON.stringify({
        storyId: parseInt(storyId),  // 尝试数字格式
        adult: 0
    });

    const options = {
        hostname: 'tonofura-web-w.deepone-online.com',
        port: 443,
        path: '/deep-one/api/story/addStory',
        method: 'POST',
        headers: {
            'User-Agent': 'Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36 Edg/*********',
            'Content-Type': 'application/json;charset=UTF-8',
            'Accept': 'application/json;charset=UTF-8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6',
            'Cache-Control': 'no-cache',
            'Pragma': 'no-cache',
            'Origin': 'https://tonofura-w-cdn-client.deepone-online.com',
            'Referer': 'https://tonofura-w-cdn-client.deepone-online.com/',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Site': 'same-site',
            'X-Deep-One-App-Version': '{"masterVersion":"1.273.0","webVersion":"1.273.0","apkHotUpdateVersion":"1.273.0"}',
            'Content-Length': Buffer.byteLength(requestBody),
            'Authorization': authHeader
        }
    };

    return new Promise((resolve, reject) => {
        const req = https.request(options, (res) => {
            let data = '';

            res.on('data', (chunk) => {
                data += chunk;
            });

            res.on('end', () => {
                if (res.statusCode === 200) {
                    try {
                        const jsonData = JSON.parse(data);
                        console.log('✅ 不同格式添加Story成功！');
                        resolve(jsonData);
                    } catch (parseError) {
                        reject(parseError);
                    }
                } else {
                    console.log(`❌ 不同格式添加Story失败 (HTTP ${res.statusCode}): ${data}`);
                    reject(new Error(`HTTP ${res.statusCode}: ${data}`));
                }
            });
        });

        req.on('error', (error) => {
            reject(error);
        });

        req.write(requestBody);
        req.end();
    });
}

// 改进的 addStory 函数
async function addStoryImproved(storyId) {
    console.log(`📝 改进版添加Story ID: ${storyId}`);

    // 读取认证信息
    const authHeader = getAuthFromFile();
    if (!authHeader) {
        console.log('❌ 请先在 auth.txt 中设置OAuth认证信息');
        return false;
    }

    const methods = [
        () => tryAddStoryDirect(storyId, authHeader),
        () => tryAddStoryAlternatePath(storyId, authHeader),
        () => tryAddStoryDifferentFormat(storyId, authHeader)
    ];

    for (let i = 0; i < methods.length; i++) {
        try {
            console.log(`\n🔄 尝试方法 ${i + 1}/${methods.length}...`);
            const result = await methods[i]();
            console.log(`✅ 方法 ${i + 1} 成功！`);
            console.log('📄 响应数据:', JSON.stringify(result, null, 2));
            return result;
        } catch (error) {
            console.log(`⚠️  方法 ${i + 1} 失败: ${error.message}`);
            
            // 如果是最后一个方法，抛出错误
            if (i === methods.length - 1) {
                throw new Error(`所有 ${methods.length} 种方法都失败了。最后错误: ${error.message}`);
            }
        }
    }
}

module.exports = { addStoryImproved };

// 如果直接运行此文件，进行测试
if (require.main === module) {
    const testStoryId = process.argv[2] || '100503';
    
    console.log('🧪 测试改进版addStory功能...\n');
    
    addStoryImproved(testStoryId)
        .then(result => {
            console.log('\n✅ 测试成功！');
        })
        .catch(error => {
            console.log('\n❌ 测试失败:', error.message);
        });
}
