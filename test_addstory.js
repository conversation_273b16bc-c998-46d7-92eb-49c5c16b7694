/**
 * 测试addStory功能
 */

const { getStory, addStory, getPictureBookDetail } = require('./get_story');

async function testAddStory() {
    console.log('=== 测试addStory功能 ===\n');
    
    const testStoryId = '100503'; // 使用一个测试Story ID
    
    try {
        console.log('🧪 测试1: 单独调用addStory');
        const addResult = await addStory(testStoryId);
        console.log('✅ addStory测试成功:', addResult);
        console.log('');
        
        console.log('🧪 测试2: 完整流程 (addStory + getResource)');
        const storyResult = await getStory(testStoryId);
        console.log('✅ 完整流程测试成功');
        
    } catch (error) {
        console.log('❌ 测试失败:', error.message);
        console.log('\n💡 可能的原因:');
        console.log('1. auth.txt文件不存在或认证信息无效');
        console.log('2. addStory API端点可能不正确');
        console.log('3. 网络连接问题');
        console.log('4. Story ID不存在');
        
        console.log('\n🔧 调试信息:');
        console.log('- 确保auth.txt文件存在且包含有效的OAuth信息');
        console.log('- 检查网络连接');
        console.log('- 尝试不同的Story ID');
    }
}

async function testDifferentApis() {
    console.log('\n=== 测试不同的API路径 ===\n');
    
    const testStoryId = '100503';
    const apiPaths = [
        '/deep-one/api/story/addStory',
        '/deep-one/api/story/add',
        '/api/story/addStory',
        '/api/story/add',
        '/story/addStory',
        '/story/add'
    ];
    
    for (const apiPath of apiPaths) {
        console.log(`🔍 测试API路径: ${apiPath}`);
        
        try {
            // 这里可以修改addStory函数来测试不同的API路径
            // 暂时只是显示测试意图
            console.log(`   ⏳ 测试中...`);
            console.log(`   ❓ 需要修改addStory函数来测试此路径`);
            
        } catch (error) {
            console.log(`   ❌ 失败: ${error.message}`);
        }
        
        console.log('');
    }
}

// 主函数
async function main() {
    console.log('深海传说 addStory功能测试\n');
    
    // 检查auth.txt文件
    const fs = require('fs');
    if (!fs.existsSync('auth.txt')) {
        console.log('❌ 未找到auth.txt文件');
        console.log('\n📋 请先创建auth.txt文件并添加OAuth认证信息:');
        console.log('1. 在浏览器中打开深海传说游戏');
        console.log('2. 按F12 → Network → 查看Story相关请求');
        console.log('3. 复制authorization字段的值到auth.txt文件中');
        return;
    }
    
    await testAddStory();
    await testDifferentApis();
    
    console.log('\n=== 测试完成 ===');
    console.log('💡 如果addStory失败，这是正常的，因为我们在推测API端点');
    console.log('💡 主要功能是确保getStory仍然正常工作');
}

if (require.main === module) {
    main().catch(console.error);
}

module.exports = { testAddStory, testDifferentApis };
