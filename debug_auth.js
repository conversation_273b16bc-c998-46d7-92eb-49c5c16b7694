/**
 * 调试认证头解析工具
 */

const { parseAuthFromText } = require('./easy_test');
const fs = require('fs');

function debugAuth() {
    console.log('=== 认证头调试工具 ===\n');
    
    // 从auth.txt读取
    let authText = '';
    try {
        authText = fs.readFileSync('auth.txt', 'utf8');
        console.log('📁 从 auth.txt 读取的原始内容:');
        console.log('---');
        console.log(authText);
        console.log('---\n');
    } catch (error) {
        console.log('❌ 无法读取 auth.txt 文件');
        return;
    }
    
    // 解析认证信息
    const oauthParams = parseAuthFromText(authText);
    
    if (!oauthParams || Object.keys(oauthParams).length === 0) {
        console.log('❌ 解析失败，未找到有效的OAuth参数');
        return;
    }
    
    console.log('✅ 解析成功！找到以下OAuth参数:');
    Object.entries(oauthParams).forEach(([key, value]) => {
        if (key === 'oauth_signature') {
            console.log(`  ${key}: ${value.substring(0, 20)}...`);
        } else {
            console.log(`  ${key}: ${value}`);
        }
    });
    
    // 生成Authorization头
    console.log('\n🔧 生成的Authorization头:');
    const authParts = Object.entries(oauthParams)
        .filter(([key, value]) => value !== null && value !== undefined && key !== 'realm')
        .map(([key, value]) => `${key}="${value}"`)
        .join(' ');
    
    const authHeader = `OAuth realm="Users" ${authParts}`;
    console.log(authHeader);
    
    // 检查常见问题
    console.log('\n🔍 检查常见问题:');
    
    const requiredParams = ['oauth_token', 'oauth_signature', 'oauth_consumer_key'];
    const missingParams = requiredParams.filter(param => !oauthParams[param]);
    
    if (missingParams.length > 0) {
        console.log(`❌ 缺少必需参数: ${missingParams.join(', ')}`);
    } else {
        console.log('✅ 所有必需参数都存在');
    }
    
    // 检查时间戳
    if (oauthParams.oauth_timestamp) {
        const timestamp = parseInt(oauthParams.oauth_timestamp);
        const now = Math.floor(Date.now() / 1000);
        const age = now - timestamp;
        
        console.log(`⏰ 时间戳年龄: ${age} 秒`);
        if (age > 300) { // 5分钟
            console.log('⚠️  警告: 时间戳可能已过期（超过5分钟）');
        } else {
            console.log('✅ 时间戳看起来是新的');
        }
    }
    
    // 检查签名长度
    if (oauthParams.oauth_signature) {
        const sigLength = oauthParams.oauth_signature.length;
        console.log(`🔐 签名长度: ${sigLength} 字符`);
        if (sigLength < 20) {
            console.log('⚠️  警告: 签名看起来太短');
        }
    }
    
    console.log('\n💡 建议:');
    if (missingParams.length > 0) {
        console.log('- 重新从浏览器复制完整的authorization头');
    } else if (oauthParams.oauth_timestamp && (Math.floor(Date.now() / 1000) - parseInt(oauthParams.oauth_timestamp)) > 300) {
        console.log('- 认证信息可能已过期，请获取最新的authorization头');
    } else {
        console.log('- 认证信息看起来正常，可以尝试发送请求');
    }
}

// 测试不同的输入格式
function testFormats() {
    console.log('\n=== 测试不同输入格式 ===\n');
    
    const testCases = [
        {
            name: '标准格式',
            input: 'OAuth realm="Users" oauth_token="abc123" oauth_signature="xyz789"'
        },
        {
            name: '带authorization前缀',
            input: 'authorization: OAuth realm="Users" oauth_token="abc123" oauth_signature="xyz789"'
        },
        {
            name: '多行格式',
            input: `OAuth realm="Users" 
                   oauth_token="abc123" 
                   oauth_signature="xyz789"`
        },
        {
            name: '带转义字符',
            input: 'OAuth realm=\\"Users\\" oauth_token=\\"abc123\\" oauth_signature=\\"xyz789\\"'
        }
    ];
    
    testCases.forEach(testCase => {
        console.log(`📝 测试: ${testCase.name}`);
        console.log(`输入: ${testCase.input.replace(/\s+/g, ' ')}`);
        
        const result = parseAuthFromText(testCase.input);
        if (result && Object.keys(result).length > 0) {
            console.log('✅ 解析成功:', Object.keys(result).join(', '));
        } else {
            console.log('❌ 解析失败');
        }
        console.log('');
    });
}

function main() {
    const args = process.argv.slice(2);
    
    if (args.includes('--test')) {
        testFormats();
    } else {
        debugAuth();
    }
}

if (require.main === module) {
    main();
}

module.exports = { debugAuth, testFormats };
