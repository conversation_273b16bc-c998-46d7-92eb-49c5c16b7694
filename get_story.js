/**
 * 深海传说 Story获取工具 - 增强版
 *
 * 功能:
 * - 自动先调用addStory添加故事到用户账户
 * - 然后调用getResource获取故事资源
 *
 * 使用方法:
 * 1. 编辑 auth.txt，粘贴你的OAuth字符串
 * 2. 运行: node get_story.js [story_id]
 *
 * 流程:
 * 1. 调用addStory API添加故事
 * 2. 调用getResource API获取资源
 */

const https = require('https');
const fs = require('fs');

function getAuthFromFile() {
    try {
        const content = fs.readFileSync('auth.txt', 'utf8');
        const lines = content.split('\n');

        for (const line of lines) {
            const trimmed = line.trim();
            if (trimmed.startsWith('OAuth') && !trimmed.startsWith('#')) {
                return trimmed;
            }
        }
        return null;
    } catch (error) {
        return null;
    }
}

async function getPictureBookDetail(storyId) {
    console.log(`📖 获取PictureBook详情 ID: ${storyId}`);

    // 读取认证信息
    const authHeader = getAuthFromFile();
    if (!authHeader) {
        console.log('❌ 请先在 auth.txt 中设置OAuth认证信息');
        return false;
    }

    // 构建请求 - 获取pictureBookDetail
    const requestBody = JSON.stringify({
        storyId: storyId
    });

    const options = {
        hostname: 'tonofura-web-w.deepone-online.com',
        port: 443,
        path: '/deep-one/api/story/getPictureBookDetail',  // 推测的API路径
        method: 'POST',
        headers: {
            'User-Agent': 'Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36 Edg/*********',
            'Content-Type': 'application/json;charset=UTF-8',
            'Accept': 'application/json;charset=UTF-8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6',
            'Cache-Control': 'no-cache',
            'Pragma': 'no-cache',
            'Origin': 'https://tonofura-w-cdn-client.deepone-online.com',
            'Referer': 'https://tonofura-w-cdn-client.deepone-online.com/',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Site': 'same-site',
            'X-Deep-One-App-Version': '{"masterVersion":"1.273.0","webVersion":"1.273.0","apkHotUpdateVersion":"1.273.0"}',
            'Content-Length': Buffer.byteLength(requestBody),
            'Authorization': authHeader
        }
    };

    return new Promise((resolve, reject) => {
        console.log('🚀 发送getPictureBookDetail请求...');

        const req = https.request(options, (res) => {
            let data = '';

            res.on('data', (chunk) => {
                data += chunk;
            });

            res.on('end', () => {
                if (res.statusCode === 200) {
                    try {
                        const jsonData = JSON.parse(data);
                        console.log('✅ 成功获取PictureBook详情！');
                        resolve(jsonData);
                    } catch (parseError) {
                        console.log(`❌ JSON解析失败: ${parseError.message}`);
                        reject(parseError);
                    }
                } else {
                    console.log(`❌ getPictureBookDetail请求失败 (HTTP ${res.statusCode}): ${data}`);
                    reject(new Error(`HTTP ${res.statusCode}: ${data}`));
                }
            });
        });

        req.on('error', (error) => {
            console.log(`❌ 网络错误: ${error.message}`);
            reject(error);
        });

        req.write(requestBody);
        req.end();
    });
}

async function addStory(storyId) {
    console.log(`📝 添加Story ID: ${storyId}`);

    // 读取认证信息
    const authHeader = getAuthFromFile();
    if (!authHeader) {
        console.log('❌ 请先在 auth.txt 中设置OAuth认证信息');
        return false;
    }

    // 步骤1: 先获取pictureBookDetail
    try {
        console.log('📖 步骤1a: 先获取PictureBook详情...');
        const pictureBookDetail = await getPictureBookDetail(storyId);
        console.log('✅ PictureBook详情获取成功');
    } catch (error) {
        console.log(`⚠️  获取PictureBook详情失败: ${error.message}`);
        console.log('🔄 继续尝试直接添加Story...');
    }

    // 步骤2: 添加Story
    console.log('📝 步骤1b: 添加Story到账户...');

    // 构建请求 - 基于客户端的addStory函数推测
    const requestBody = JSON.stringify({
        storyId: storyId
    });

    const options = {
        hostname: 'tonofura-web-w.deepone-online.com',
        port: 443,
        path: '/deep-one/api/story/addStory',  // 推测的API路径
        method: 'POST',
        headers: {
            'User-Agent': 'Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36 Edg/*********',
            'Content-Type': 'application/json;charset=UTF-8',
            'Accept': 'application/json;charset=UTF-8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6',
            'Cache-Control': 'no-cache',
            'Pragma': 'no-cache',
            'Origin': 'https://tonofura-w-cdn-client.deepone-online.com',
            'Referer': 'https://tonofura-w-cdn-client.deepone-online.com/',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Site': 'same-site',
            'X-Deep-One-App-Version': '{"masterVersion":"1.273.0","webVersion":"1.273.0","apkHotUpdateVersion":"1.273.0"}',
            'Content-Length': Buffer.byteLength(requestBody),
            'Authorization': authHeader
        }
    };

    return new Promise((resolve, reject) => {
        console.log('🚀 发送addStory请求...');

        const req = https.request(options, (res) => {
            let data = '';

            res.on('data', (chunk) => {
                data += chunk;
            });

            res.on('end', () => {
                if (res.statusCode === 200) {
                    try {
                        const jsonData = JSON.parse(data);
                        console.log('✅ 成功添加Story！');
                        console.log('📄 响应数据:', JSON.stringify(jsonData, null, 2));
                        resolve(jsonData);
                    } catch (parseError) {
                        console.log(`❌ JSON解析失败: ${parseError.message}`);
                        reject(parseError);
                    }
                } else {
                    console.log(`❌ addStory请求失败 (HTTP ${res.statusCode}): ${data}`);

                    if (res.statusCode === 401) {
                        console.log('\n💡 认证失败，请重新获取OAuth信息');
                    } else if (res.statusCode === 400) {
                        console.log('\n💡 请求格式错误，请检查Story ID');
                    } else if (res.statusCode === 404) {
                        console.log('\n💡 API端点不存在，可能需要调整路径');
                    }

                    reject(new Error(`HTTP ${res.statusCode}: ${data}`));
                }
            });
        });

        req.on('error', (error) => {
            console.log(`❌ 网络错误: ${error.message}`);
            reject(error);
        });

        req.write(requestBody);
        req.end();
    });
}

async function getStory(storyId) {
    console.log(`🎯 获取Story ID: ${storyId}`);

    // 读取认证信息
    const authHeader = getAuthFromFile();
    if (!authHeader) {
        console.log('❌ 请先在 auth.txt 中设置OAuth认证信息');
        console.log('\n📋 步骤:');
        console.log('1. 在浏览器中打开深海传说游戏');
        console.log('2. 按F12 → Network → 查看Story → 找到 /deep-one/api/story/getResource 请求');
        console.log('3. 复制 authorization 字段的值到 auth.txt 文件中');
        return;
    }

    // 先调用addStory添加故事
    try {
        console.log('\n📝 步骤1: 先添加Story到用户账户...');
        await addStory(storyId);
        console.log('✅ Story添加成功，现在获取资源...\n');
    } catch (error) {
        console.log(`⚠️  addStory失败: ${error.message}`);
        console.log('🔄 继续尝试获取资源...\n');
    }

    // 步骤2: 获取Story资源
    console.log('📥 步骤2: 获取Story资源...');

    // 构建请求
    const requestBody = JSON.stringify({
        storyIds: storyId,
        adult: 1
    });

    const options = {
        hostname: 'tonofura-web-w.deepone-online.com',
        port: 443,
        path: '/deep-one/api/story/getResource',
        method: 'POST',
        headers: {
            'User-Agent': 'Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36 Edg/*********',
            'Content-Type': 'application/json;charset=UTF-8',
            'Accept': 'application/json;charset=UTF-8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6',
            'Cache-Control': 'no-cache',
            'Pragma': 'no-cache',
            'Origin': 'https://tonofura-w-cdn-client.deepone-online.com',
            'Referer': 'https://tonofura-w-cdn-client.deepone-online.com/',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Site': 'same-site',
            'X-Deep-One-App-Version': '{"masterVersion":"1.273.0","webVersion":"1.273.0","apkHotUpdateVersion":"1.273.0"}',
            'Content-Length': Buffer.byteLength(requestBody),
            'Authorization': authHeader
        }
    };
    
    return new Promise((resolve, reject) => {
        console.log('🚀 发送请求...');
        
        const req = https.request(options, (res) => {
            let data = '';
            
            res.on('data', (chunk) => {
                data += chunk;
            });
            
            res.on('end', () => {
                if (res.statusCode === 200) {
                    try {
                        const jsonData = JSON.parse(data);
                        console.log('✅ 成功获取Story数据！');
                        
                        // 保存数据
                        const filename = `story_${storyId}_${Date.now()}.json`;
                        fs.writeFileSync(filename, JSON.stringify(jsonData, null, 2));
                        console.log(`📁 数据已保存到: ${filename}`);
                        
                        // 显示资源摘要
                        if (jsonData.resource && Array.isArray(jsonData.resource)) {
                            console.log(`\n📊 包含 ${jsonData.resource.length} 个资源文件:`);
                            
                            const textFiles = jsonData.resource.filter(r => r.fileName.endsWith('.txt'));
                            const voiceFiles = jsonData.resource.filter(r => r.fileName.endsWith('.mp3'));
                            const imageFiles = jsonData.resource.filter(r => r.fileName.match(/\.(jpg|png|webp)$/));
                            
                            if (textFiles.length > 0) {
                                console.log(`  📝 文本文件: ${textFiles.length} 个`);
                                textFiles.forEach(f => console.log(`    - ${f.fileName}`));
                            }
                            
                            if (voiceFiles.length > 0) {
                                console.log(`  🎵 语音文件: ${voiceFiles.length} 个`);
                            }
                            
                            if (imageFiles.length > 0) {
                                console.log(`  🖼️  图片文件: ${imageFiles.length} 个`);
                            }
                        }
                        
                        resolve(jsonData);
                    } catch (parseError) {
                        console.log(`❌ JSON解析失败: ${parseError.message}`);
                        reject(parseError);
                    }
                } else {
                    console.log(`❌ 请求失败 (HTTP ${res.statusCode}): ${data}`);
                    
                    if (res.statusCode === 401) {
                        console.log('\n💡 认证失败，请重新获取OAuth信息');
                    } else if (res.statusCode === 400) {
                        console.log('\n💡 请求格式错误，请检查Story ID');
                    } else if (res.statusCode === 404) {
                        console.log('\n💡 Story不存在，请尝试其他ID');
                    }
                    
                    reject(new Error(`HTTP ${res.statusCode}: ${data}`));
                }
            });
        });
        
        req.on('error', (error) => {
            console.log(`❌ 网络错误: ${error.message}`);
            reject(error);
        });
        
        req.write(requestBody);
        req.end();
    });
}

// 批量获取多个Story
async function getMultipleStories(storyIds) {
    console.log(`🎯 批量获取 ${storyIds.length} 个Story\n`);
    
    const results = [];
    
    for (let i = 0; i < storyIds.length; i++) {
        const storyId = storyIds[i];
        
        try {
            console.log(`[${i + 1}/${storyIds.length}] 获取Story ${storyId}`);
            const result = await getStory(storyId);
            results.push({ storyId, success: true, data: result });
            
            // 添加延迟避免请求过快
            if (i < storyIds.length - 1) {
                console.log('⏳ 等待2秒...\n');
                await new Promise(resolve => setTimeout(resolve, 2000));
            }
            
        } catch (error) {
            console.log(`❌ Story ${storyId} 获取失败: ${error.message}\n`);
            results.push({ storyId, success: false, error: error.message });
        }
    }
    
    // 显示总结
    const successful = results.filter(r => r.success).length;
    const failed = results.length - successful;
    
    console.log('\n📊 批量获取完成:');
    console.log(`✅ 成功: ${successful} 个`);
    console.log(`❌ 失败: ${failed} 个`);
    
    if (failed > 0) {
        console.log('\n失败的Story ID:');
        results.filter(r => !r.success).forEach(r => {
            console.log(`  - ${r.storyId}: ${r.error}`);
        });
    }
    
    return results;
}

function main() {
    const args = process.argv.slice(2);
    
    if (args.length === 0) {
        console.log('深海传说 Story获取工具\n');
        console.log('使用方法:');
        console.log('  node get_story.js <story_id>           # 获取单个Story');
        console.log('  node get_story.js <id1> <id2> <id3>    # 获取多个Story');
        console.log('  node get_story.js 100501-100510        # 获取范围内的Story');
        console.log('');
        console.log('示例:');
        console.log('  node get_story.js 100503');
        console.log('  node get_story.js 100501 100502 100503');
        console.log('  node get_story.js 100501-100505');
        console.log('');
        console.log('首次使用请先设置 auth.txt 文件');
        return;
    }
    
    let storyIds = [];
    
    // 解析参数
    for (const arg of args) {
        if (arg.includes('-')) {
            // 范围格式: 100501-100505
            const [start, end] = arg.split('-').map(Number);
            if (start && end && start <= end) {
                for (let i = start; i <= end; i++) {
                    storyIds.push(i.toString());
                }
            } else {
                console.log(`❌ 无效的范围格式: ${arg}`);
                return;
            }
        } else {
            // 单个ID
            storyIds.push(arg);
        }
    }
    
    if (storyIds.length === 1) {
        getStory(storyIds[0]).catch(console.error);
    } else {
        getMultipleStories(storyIds).catch(console.error);
    }
}

if (require.main === module) {
    main();
}

module.exports = { getStory, getMultipleStories, addStory, getPictureBookDetail };
