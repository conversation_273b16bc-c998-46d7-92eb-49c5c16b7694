# 深海传说 Story获取工具 - 最终版本

## 🎉 问题已解决！

经过调试，我们已经成功解决了引号转义和认证问题。现在可以正常获取Story资源了！

## 🚀 超简单使用方法

### 第一步：获取认证信息（一次性）

1. 在浏览器中打开深海传说游戏
2. 按 `F12` 打开开发者工具
3. 点击 "Network"（网络）标签
4. 在游戏中查看任意Story
5. 找到 `/deep-one/api/story/getResource` 请求
6. 复制 `authorization` 字段的完整值

### 第二步：设置认证文件

编辑 `auth.txt` 文件，粘贴你复制的OAuth字符串：

```
OAuth realm="Users" oauth_token="你的token" xoauth_requestor_id="你的requestor_id" oauth_consumer_key="*********************************" oauth_signature_method="HMAC-SHA256" oauth_nonce="你的nonce" oauth_timestamp="你的timestamp" oauth_signature="你的signature"
```

### 第三步：获取Story

```bash
# 获取单个Story
node get_story.js 100503

# 获取多个Story
node get_story.js 100501 100502 100503

# 获取范围内的Story
node get_story.js 100501-100510
```

## 📊 成功示例

```
🎯 获取Story ID: 100503
🚀 发送请求...
✅ 成功获取Story数据！
📁 数据已保存到: story_100503_1755926786167.json

📊 包含 7 个资源文件:
  📝 文本文件: 1 个
    - download/adv/text/character/1005/adultw/100503.txt
  🎵 语音文件: 6 个
```

## 📁 获取到的数据

每个Story包含：
- **文本文件** (.txt) - Story的完整文本内容
- **语音文件** (.mp3) - 角色配音
- **图片文件** (.jpg/.png) - 背景图片、角色立绘等
- **路径和MD5** - 用于下载和校验文件

## 🔧 可用工具

1. **`get_story.js`** - 最终简化版本（推荐）
2. **`test_precise.js`** - 精确测试工具
3. **`debug_auth.js`** - 认证信息调试工具
4. **`easy_test.js`** - 多种输入方式的测试工具

## 💡 常见问题

### Q: 提示401认证失败？
A: OAuth签名会很快过期，重新从浏览器获取最新的authorization头即可。

### Q: 如何知道有哪些Story ID？
A: 可以尝试这些常见模式：
- `100501-100510` - 主线Story
- `200001-200010` - 活动Story
- `300001-300010` - 特殊Story

### Q: 如何批量获取？
A: 
```bash
# 方法1: 范围获取
node get_story.js 100501-100510

# 方法2: 指定多个ID
node get_story.js 100501 100502 100503 100504 100505
```

### Q: 获取的文件如何使用？
A: JSON文件包含了资源的下载路径和MD5，可以：
- 分析Story结构和内容
- 下载对应的文本和语音文件
- 用于游戏数据分析

## 🎯 技术细节

### API端点
- **URL**: `https://tonofura-web-w.deepone-online.com/deep-one/api/story/getResource`
- **方法**: POST
- **格式**: JSON

### 请求体
```json
{
  "storyIds": "100503",
  "adult": 0
}
```

### 响应格式
```json
{
  "storyIds": [100503],
  "adult": 0,
  "resource": [
    {
      "fileName": "download/adv/text/character/1005/adultw/100503.txt",
      "path": "94/94",
      "md5": "589b9f287b206914edee68c03d82dcd5"
    }
  ]
}
```

## 🏆 成就解锁

✅ **API端点确认** - 找到正确的服务器地址和路径  
✅ **认证方式确认** - OAuth 1.0a签名认证  
✅ **请求格式确认** - JSON POST请求  
✅ **引号转义问题解决** - 支持多种输入格式  
✅ **成功获取数据** - 完整的Story资源信息  

现在你可以轻松获取深海传说的Story资源了！🎉
