/**
 * 精确测试工具 - 直接使用原始Authorization头
 */

const https = require('https');
const fs = require('fs');

function parseAuthFromFile() {
    try {
        const content = fs.readFileSync('auth.txt', 'utf8');
        
        // 查找OAuth行
        const lines = content.split('\n');
        for (const line of lines) {
            const trimmed = line.trim();
            if (trimmed.startsWith('OAuth') && !trimmed.startsWith('#')) {
                return trimmed;
            }
        }
        return null;
    } catch (error) {
        return null;
    }
}

async function testPreciseRequest(storyId = '100503') {
    console.log('=== 精确测试工具 ===\n');
    
    // 直接从文件读取OAuth字符串
    const authHeader = parseAuthFromFile();
    
    if (!authHeader) {
        console.log('❌ 无法从 auth.txt 读取OAuth信息');
        console.log('请确保 auth.txt 中有以 OAuth 开头的行');
        return;
    }
    
    console.log('📋 使用的Authorization头:');
    console.log(authHeader);
    console.log('');
    
    // 构建请求
    const requestBody = JSON.stringify({
        storyIds: storyId,
        adult: 0
    });
    
    const options = {
        hostname: 'tonofura-web-w.deepone-online.com',
        port: 443,
        path: '/deep-one/api/story/getResource',
        method: 'POST',
        headers: {
            'User-Agent': 'Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36 Edg/*********',
            'Content-Type': 'application/json;charset=UTF-8',
            'Accept': 'application/json;charset=UTF-8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6',
            'Accept-Encoding': 'gzip, deflate, br, zstd',
            'Cache-Control': 'no-cache',
            'Pragma': 'no-cache',
            'Origin': 'https://tonofura-w-cdn-client.deepone-online.com',
            'Referer': 'https://tonofura-w-cdn-client.deepone-online.com/',
            'Sec-Ch-Ua': '"Not;A=Brand";v="99", "Microsoft Edge";v="139", "Chromium";v="139"',
            'Sec-Ch-Ua-Mobile': '?1',
            'Sec-Ch-Ua-Platform': '"Android"',
            'Sec-Fetch-Dest': 'empty',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Site': 'same-site',
            'Priority': 'u=1, i',
            'X-Deep-One-App-Version': '{"masterVersion":"1.273.0","webVersion":"1.273.0","apkHotUpdateVersion":"1.273.0"}',
            'Content-Length': Buffer.byteLength(requestBody),
            'Authorization': authHeader  // 直接使用原始字符串
        }
    };
    
    console.log('🚀 发送请求...');
    console.log(`URL: https://${options.hostname}${options.path}`);
    console.log(`Body: ${requestBody}`);
    console.log('');
    
    return new Promise((resolve, reject) => {
        const req = https.request(options, (res) => {
            let data = '';
            
            res.on('data', (chunk) => {
                data += chunk;
            });
            
            res.on('end', () => {
                console.log(`📊 响应状态码: ${res.statusCode}`);
                console.log(`📄 响应头:`, JSON.stringify(res.headers, null, 2));
                console.log(`📝 响应数据: ${data}`);
                
                if (res.statusCode === 200) {
                    try {
                        const jsonData = JSON.parse(data);
                        console.log('\n✅ 成功获取Story数据！');
                        
                        // 保存数据
                        const filename = `story_${storyId}_${Date.now()}.json`;
                        fs.writeFileSync(filename, JSON.stringify(jsonData, null, 2));
                        console.log(`📁 数据已保存到: ${filename}`);
                        
                        // 显示数据结构
                        console.log('\n📊 数据结构:');
                        if (jsonData && typeof jsonData === 'object') {
                            console.log(`- 主要字段: ${Object.keys(jsonData).join(', ')}`);
                            if (jsonData.data) {
                                console.log(`- data字段类型: ${typeof jsonData.data}`);
                                if (typeof jsonData.data === 'object') {
                                    console.log(`- data内容: ${Object.keys(jsonData.data).join(', ')}`);
                                }
                            }
                        }
                        
                        resolve(jsonData);
                    } catch (parseError) {
                        console.log(`❌ JSON解析失败: ${parseError.message}`);
                        reject(parseError);
                    }
                } else {
                    console.log(`\n❌ 请求失败 (HTTP ${res.statusCode})`);
                    
                    // 分析错误
                    if (res.statusCode === 400) {
                        console.log('\n🔍 400 Bad Request 可能的原因:');
                        console.log('- 请求体格式错误');
                        console.log('- Authorization头格式错误');
                        console.log('- 缺少必需的请求头');
                        console.log('- Story ID格式错误');
                    } else if (res.statusCode === 401) {
                        console.log('\n🔍 401 Unauthorized 可能的原因:');
                        console.log('- OAuth签名已过期');
                        console.log('- oauth_token无效');
                        console.log('- 认证信息不完整');
                    } else if (res.statusCode === 403) {
                        console.log('\n🔍 403 Forbidden 可能的原因:');
                        console.log('- 没有访问该Story的权限');
                        console.log('- 账号被限制');
                    } else if (res.statusCode === 404) {
                        console.log('\n🔍 404 Not Found 可能的原因:');
                        console.log('- Story ID不存在');
                        console.log('- API端点路径错误');
                    }
                    
                    reject(new Error(`HTTP ${res.statusCode}: ${data}`));
                }
            });
        });
        
        req.on('error', (error) => {
            console.log(`❌ 网络错误: ${error.message}`);
            reject(error);
        });
        
        req.write(requestBody);
        req.end();
    });
}

// 测试多个Story ID
async function testMultipleStories() {
    const storyIds = ['100503', '100501', '100502', '100504', '100505'];
    
    console.log('\n=== 测试多个Story ID ===\n');
    
    for (const storyId of storyIds) {
        try {
            console.log(`\n--- 测试Story ID: ${storyId} ---`);
            await testPreciseRequest(storyId);
            
            // 添加延迟避免请求过快
            console.log('等待2秒...');
            await new Promise(resolve => setTimeout(resolve, 2000));
            
        } catch (error) {
            console.log(`Story ${storyId} 失败: ${error.message}`);
        }
    }
}

function main() {
    const args = process.argv.slice(2);
    const storyId = args[0] || '100503';
    
    if (args.includes('--multiple')) {
        testMultipleStories().catch(console.error);
    } else {
        testPreciseRequest(storyId).catch(console.error);
    }
}

if (require.main === module) {
    main();
}

module.exports = { testPreciseRequest, testMultipleStories };
